# Freqtrade项目开发规则

## 项目概述
这是一个基于Docker部署的Freqtrade加密货币交易机器人项目，专注于期货交易，使用多种技术分析策略进行自动化交易。

## 项目结构
```
freqtrade_3/
├── user_data/                    # 用户数据目录
│   ├── strategies/               # 策略文件目录
│   ├── config*.json             # 配置文件
│   ├── data/                    # 历史数据
│   ├── backtest_results/        # 回测结果
│   ├── hyperopt_results/        # 参数优化结果
│   └── logs/                    # 日志文件
├── docs/                        # 文档目录
├── tests/                       # 测试文件
├── docker-compose.yml           # Docker配置
└── *.pine                       # Pine脚本参考文件
```

## 策略开发规范

### 1. 策略文件结构
- 所有策略必须继承自`IStrategy`类
- 使用`INTERFACE_VERSION = 3`支持杠杆和做空
- 必须实现的方法：
  - `populate_indicators()` - 技术指标计算
  - `populate_entry_trend()` - 入场信号
  - `populate_exit_trend()` - 出场信号
- 推荐实现的方法：
  - `leverage()` - 自定义杠杆设置
  - `custom_stoploss()` - 自定义止损
  - `protections` - 保护机制

### 2. 参数优化规范
- 使用`IntParameter`, `DecimalParameter`, `BooleanParameter`定义可优化参数
- 杠杆范围：2-28倍，使用`IntParameter(2, 28, default=8, space="buy", optimize=True)`
- 所有关键参数都应该可优化，包括：
  - 技术指标周期
  - 入场/出场阈值
  - 止盈止损参数
  - 杠杆倍数

### 3. 风险管理要求
- 必须设置止损：`stoploss = -0.xx`
- 推荐使用追踪止损：`trailing_stop = True`
- 必须实现保护机制：
  - StoplossGuard - 止损保护
  - MaxDrawdown - 最大回撤保护
  - LowProfitPairs - 低盈利交易对保护
  - CooldownPeriod - 冷却期保护

### 4. 配置文件规范
- 每个策略应有对应的配置文件：`config_策略名.json`
- 必须配置项：
  - `trading_mode: "futures"` - 期货交易模式
  - `margin_mode: "isolated"` - 隔离保证金
  - `max_open_trades` - 最大开仓数量
  - `stake_amount` - 仓位大小
  - 交易对白名单和黑名单

## 回测和优化流程

### 1. 回测命令
```bash
docker-compose run --rm freqtrade backtesting \
  --strategy Strategy_name \
  --config /user_data/config.json \
  --timeframe 1h \
  --timerange=20240101-20250801
```

### 2. 参数优化命令
```bash
docker-compose run --rm freqtrade hyperopt \
  --strategy Strategy_name \
  --hyperopt-loss SharpeHyperOptLoss \
  --spaces buy sell \
  --epochs 500 \
  --timerange=20240101-20250801
```

### 3. 优化建议
- 建议运行500-1000个epoch为一轮
- 总共至少运行10000个epoch
- 使用贝叶斯搜索，避免过度优化
- 优化空间包括：buy, sell, roi, stoploss, trailing

## 策略逻辑一致性检查

### 1. Pine脚本对比
- HMA Crossover策略：基于快慢HMA交叉，结合ATR止损
- Holy Grail策略：基于SMA的趋势跟踪策略
- RSI+Breakout策略：结合RSI均值回归和突破策略

### 2. Python实现要求
- 确保入场出场逻辑与Pine脚本一致
- 技术指标计算方法保持一致
- 风险管理参数对应转换

## 数据和环境

### 1. 数据源
- 交易所：Binance期货
- 数据存储：`user_data/data/binance/futures/`
- 支持的时间框架：1m, 5m, 15m, 30m, 1h, 4h

### 2. 交易对
当前支持的交易对：
- BTC/USDT:USDT
- ETH/USDT:USDT  
- SOL/USDT:USDT
- SUI/USDT:USDT
- DOGE/USDT:USDT
- XRP/USDT:USDT

## 开发工作流

### 1. 策略开发步骤
1. 分析Pine脚本逻辑
2. 创建Python策略文件
3. 创建对应配置文件
4. 进行回测验证
5. 参数优化
6. 性能分析和调优

### 2. 质量保证
- 所有策略必须通过回测
- 参数优化结果必须合理
- 风险指标在可接受范围内
- 代码符合PEP8规范

## 注意事项
- 使用Docker环境，避免直接修改系统文件
- 所有配置通过配置文件管理，不硬编码
- 定期备份重要数据和配置
- 监控日志文件，及时发现问题
- 测试环境使用dry_run模式
